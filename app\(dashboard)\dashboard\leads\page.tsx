'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Archive, RefreshCw, AlertCircle } from 'lucide-react'
import DateFilter from '@/components/leads/date-filter'
import LeadsList from '@/components/leads/leads-list'

interface Lead {
  id: string
  name: string
  phone: string
  coursePreference: string
  status: string
  source?: string
  notes?: string
  createdAt: string
  updatedAt: string
  callStartedAt?: string
  callEndedAt?: string
  callDuration?: number
  assignedAt?: string
  archivedAt?: string
  assignedGroup?: {
    name: string
    course: {
      name: string
      level: string
    }
    teacher: {
      user: {
        name: string
      }
    }
  }
  assignedTeacher?: {
    user: {
      name: string
    }
  }
  callRecords?: Array<{
    id: string
    startedAt: string
    endedAt?: string
    duration?: number
    notes?: string
  }>
}

export default function LeadsPage() {
  const [leads, setLeads] = useState<Lead[]>([])
  const [archivedLeads, setArchivedLeads] = useState<Lead[]>([])
  const [loading, setLoading] = useState(true)
  const [statusFilter, setStatusFilter] = useState('ALL')
  const [dateFilter, setDateFilter] = useState<any>({})
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('active')

  const fetchLeads = useCallback(async (archived = false) => {
    try {
      setLoading(true)
      const params = new URLSearchParams()

      if (statusFilter !== 'ALL') {
        params.append('status', statusFilter)
      }

      if (dateFilter.dateFilter) {
        params.append('dateFilter', dateFilter.dateFilter)
      } else if (dateFilter.startDate && dateFilter.endDate) {
        params.append('startDate', dateFilter.startDate)
        params.append('endDate', dateFilter.endDate)
      }

      params.append('archived', archived.toString())

      const url = `/api/leads?${params.toString()}`
      const response = await fetch(url)

      if (!response.ok) {
        throw new Error('Failed to fetch leads')
      }

      const data = await response.json()

      if (archived) {
        setArchivedLeads(data.leads || [])
      } else {
        setLeads(data.leads || [])
      }
    } catch (error) {
      console.error('Error fetching leads:', error)
      setError(error instanceof Error ? error.message : 'Failed to fetch leads')
    } finally {
      setLoading(false)
    }
  }, [statusFilter, dateFilter])

  useEffect(() => {
    fetchLeads(activeTab === 'archived')
  }, [fetchLeads, activeTab])

  const handleLeadUpdate = () => {
    fetchLeads(activeTab === 'archived')
    setError(null)
  }

  const handleError = (errorMessage: string) => {
    setError(errorMessage)
  }

  const handleDateFilterChange = (filter: any) => {
    setDateFilter(filter)
  }

  const handleRefresh = () => {
    fetchLeads(activeTab === 'archived')
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Leads Management</h1>
          <p className="text-gray-600">Comprehensive lead tracking with call management and group assignment</p>
        </div>
        <Button onClick={handleRefresh} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList>
          <TabsTrigger value="active">Active Leads</TabsTrigger>
          <TabsTrigger value="archived" className="flex items-center gap-2">
            <Archive className="h-4 w-4" />
            Archive
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="space-y-6">
          {/* Filters */}
          <div className="flex flex-wrap gap-4 items-center">
            <DateFilter
              onFilterChange={handleDateFilterChange}
              currentFilter={dateFilter.dateFilter}
            />

            <div className="flex gap-2">
              {['ALL', 'NEW', 'CALLING', 'CALL_COMPLETED', 'GROUP_ASSIGNED', 'NOT_INTERESTED'].map((status) => (
                <Button
                  key={status}
                  variant={statusFilter === status ? 'default' : 'outline'}
                  onClick={() => setStatusFilter(status)}
                  size="sm"
                >
                  {status.replace('_', ' ')}
                </Button>
              ))}
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="text-center">
                <div className="inline-block w-8 h-8 border-4 border-gray-300 border-t-blue-600 rounded-full animate-spin mb-4" />
                <p className="text-gray-600">Loading leads...</p>
              </div>
            </div>
          ) : (
            <LeadsList
              leads={leads}
              onLeadUpdate={handleLeadUpdate}
              onError={handleError}
              isArchiveView={false}
            />
          )}
        </TabsContent>

        <TabsContent value="archived" className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-semibold">Archived Leads</h2>
              <p className="text-gray-600">Leads that have been successfully assigned to groups</p>
            </div>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="text-center">
                <div className="inline-block w-8 h-8 border-4 border-gray-300 border-t-blue-600 rounded-full animate-spin mb-4" />
                <p className="text-gray-600">Loading archived leads...</p>
              </div>
            </div>
          ) : (
            <LeadsList
              leads={archivedLeads}
              onLeadUpdate={handleLeadUpdate}
              onError={handleError}
              isArchiveView={true}
            />
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
