'use client'

import { useState } from 'react'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog'
import { Phone, User, Calendar, Clock, Users, MapPin, Archive } from 'lucide-react'
import { formatDateTime, formatDate } from '@/lib/utils'
import CallManager from './call-manager'
import GroupAssignmentModal from './group-assignment-modal'

interface Lead {
  id: string
  name: string
  phone: string
  coursePreference: string
  status: string
  source?: string
  notes?: string
  createdAt: string
  updatedAt: string
  callStartedAt?: string
  callEndedAt?: string
  callDuration?: number
  assignedAt?: string
  archivedAt?: string
  assignedGroup?: {
    name: string
    course: {
      name: string
      level: string
    }
    teacher: {
      user: {
        name: string
      }
    }
  }
  assignedTeacher?: {
    user: {
      name: string
    }
  }
  callRecords?: Array<{
    id: string
    startedAt: string
    endedAt?: string
    duration?: number
    notes?: string
  }>
}

interface LeadsListProps {
  leads: Lead[]
  onLeadUpdate: () => void
  onError: (error: string) => void
  isArchiveView?: boolean
}

export default function LeadsList({ leads, onLeadUpdate, onError, isArchiveView = false }: LeadsListProps) {
  const [selectedCallLead, setSelectedCallLead] = useState<Lead | null>(null)
  const [selectedAssignLead, setSelectedAssignLead] = useState<Lead | null>(null)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'NEW':
        return 'bg-blue-100 text-blue-800'
      case 'CALLING':
        return 'bg-yellow-100 text-yellow-800'
      case 'CALL_COMPLETED':
        return 'bg-green-100 text-green-800'
      case 'GROUP_ASSIGNED':
        return 'bg-purple-100 text-purple-800'
      case 'ARCHIVED':
        return 'bg-gray-100 text-gray-800'
      case 'NOT_INTERESTED':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusText = (status: string) => {
    return status.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())
  }

  const canCall = (lead: Lead) => {
    return lead.status === 'NEW' && !isArchiveView
  }

  const canAssignGroup = (lead: Lead) => {
    return lead.status === 'CALL_COMPLETED' && !isArchiveView
  }

  const handleArchiveLead = async (leadId: string) => {
    try {
      const response = await fetch(`/api/leads/${leadId}/archive`, {
        method: 'POST',
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to archive lead')
      }

      onLeadUpdate()
    } catch (error) {
      onError(error instanceof Error ? error.message : 'Failed to archive lead')
    }
  }

  const formatCallDuration = (seconds?: number) => {
    if (!seconds) return 'N/A'
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  }

  if (leads.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500">
          {isArchiveView ? 'No archived leads found' : 'No leads found'}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {leads.map((lead) => (
        <Card key={lead.id} className="hover:shadow-md transition-shadow">
          <CardHeader className="pb-3">
            <div className="flex justify-between items-start">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h3 className="font-semibold text-lg flex items-center gap-2">
                    <User className="h-4 w-4" />
                    {lead.name}
                  </h3>
                  <Badge className={getStatusColor(lead.status)}>
                    {getStatusText(lead.status)}
                  </Badge>
                </div>
                <div className="flex items-center gap-4 text-sm text-gray-600">
                  <div className="flex items-center gap-1">
                    <Phone className="h-3 w-3" />
                    {lead.phone}
                  </div>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    {formatDate(lead.createdAt)}
                  </div>
                </div>
              </div>
              
              <div className="flex gap-2">
                {canCall(lead) && (
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button size="sm" className="bg-green-600 hover:bg-green-700">
                        <Phone className="h-4 w-4 mr-1" />
                        Call
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <CallManager
                        leadId={lead.id}
                        leadName={lead.name}
                        leadPhone={lead.phone}
                        onCallComplete={() => {
                          onLeadUpdate()
                          setSelectedCallLead(null)
                        }}
                        onError={onError}
                      />
                    </DialogContent>
                  </Dialog>
                )}

                {canAssignGroup(lead) && (
                  <Button
                    size="sm"
                    onClick={() => setSelectedAssignLead(lead)}
                    className="bg-purple-600 hover:bg-purple-700"
                  >
                    <Users className="h-4 w-4 mr-1" />
                    Choose Group
                  </Button>
                )}

                {lead.status === 'GROUP_ASSIGNED' && !isArchiveView && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleArchiveLead(lead.id)}
                  >
                    <Archive className="h-4 w-4 mr-1" />
                    Archive
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="pt-0">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <h4 className="font-medium text-sm text-gray-700 mb-1">Course Interest</h4>
                <p className="text-sm">{lead.coursePreference}</p>
              </div>

              {lead.source && (
                <div>
                  <h4 className="font-medium text-sm text-gray-700 mb-1">Source</h4>
                  <p className="text-sm">{lead.source}</p>
                </div>
              )}

              {lead.callDuration && (
                <div>
                  <h4 className="font-medium text-sm text-gray-700 mb-1">Call Duration</h4>
                  <div className="flex items-center gap-1 text-sm">
                    <Clock className="h-3 w-3" />
                    {formatCallDuration(lead.callDuration)}
                  </div>
                </div>
              )}

              {lead.assignedGroup && (
                <div className="md:col-span-2">
                  <h4 className="font-medium text-sm text-gray-700 mb-1">Assigned Group</h4>
                  <div className="space-y-1">
                    <p className="text-sm font-medium">{lead.assignedGroup.name}</p>
                    <p className="text-xs text-gray-600">
                      {lead.assignedGroup.course.name} - {lead.assignedGroup.course.level}
                    </p>
                    <p className="text-xs text-gray-600 flex items-center gap-1">
                      <User className="h-3 w-3" />
                      Teacher: {lead.assignedGroup.teacher.user.name}
                    </p>
                    {lead.assignedAt && (
                      <p className="text-xs text-gray-500">
                        Assigned: {formatDateTime(lead.assignedAt)}
                      </p>
                    )}
                  </div>
                </div>
              )}

              {lead.archivedAt && (
                <div>
                  <h4 className="font-medium text-sm text-gray-700 mb-1">Archived</h4>
                  <p className="text-xs text-gray-500">{formatDateTime(lead.archivedAt)}</p>
                </div>
              )}

              {lead.notes && (
                <div className="md:col-span-full">
                  <h4 className="font-medium text-sm text-gray-700 mb-1">Notes</h4>
                  <p className="text-sm text-gray-600">{lead.notes}</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}

      {/* Group Assignment Modal */}
      {selectedAssignLead && (
        <GroupAssignmentModal
          isOpen={!!selectedAssignLead}
          onClose={() => setSelectedAssignLead(null)}
          leadId={selectedAssignLead.id}
          leadName={selectedAssignLead.name}
          onAssignmentComplete={onLeadUpdate}
          onError={onError}
        />
      )}
    </div>
  )
}
