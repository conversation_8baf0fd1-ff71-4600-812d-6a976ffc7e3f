const { PrismaClient } = require('@prisma/client');

async function addSampleRecordings() {
  const prisma = new PrismaClient({
    datasources: {
      db: {
        url: process.env.DATABASE_URL || "postgresql://crm_owner:<EMAIL>/crm?sslmode=require"
      }
    }
  });

  try {
    console.log('Adding sample call recordings...');

    // Get some leads that have been called
    const leads = await prisma.lead.findMany({
      where: {
        status: {
          in: ['CALL_COMPLETED', 'GROUP_ASSIGNED', 'ARCHIVED']
        }
      },
      take: 3
    });

    if (leads.length === 0) {
      console.log('No leads found with call status. Creating some sample data...');
      return;
    }

    // Add sample call records with recordings
    for (let i = 0; i < leads.length; i++) {
      const lead = leads[i];
      
      // Check if call records already exist
      const existingRecords = await prisma.callRecord.findMany({
        where: { leadId: lead.id }
      });

      if (existingRecords.length === 0) {
        // Create sample call records
        await prisma.callRecord.create({
          data: {
            leadId: lead.id,
            userId: 'sample-user-id', // You might need to get a real user ID
            startedAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
            endedAt: new Date(Date.now() - 24 * 60 * 60 * 1000 + 5 * 60 * 1000), // 5 minutes later
            duration: 300, // 5 minutes
            notes: `Sample call with ${lead.name}`,
            recordingUrl: `https://www.soundjay.com/misc/sounds/bell-ringing-05.wav` // Sample audio file
          }
        });

        console.log(`Added sample recording for lead: ${lead.name}`);
      } else {
        // Update existing records to have recording URLs
        for (const record of existingRecords) {
          if (!record.recordingUrl) {
            await prisma.callRecord.update({
              where: { id: record.id },
              data: {
                recordingUrl: `https://www.soundjay.com/misc/sounds/bell-ringing-05.wav`
              }
            });
            console.log(`Updated recording URL for existing call record: ${record.id}`);
          }
        }
      }
    }

    console.log('Sample recordings added successfully!');

  } catch (error) {
    console.error('Error adding sample recordings:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script if this file is executed directly
if (require.main === module) {
  addSampleRecordings()
    .then(() => {
      console.log('Script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

module.exports = { addSampleRecordings };
