generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model User {
  id        String   @id @default(cuid())
  email     String?  @unique
  phone     String   @unique
  name      String
  role      Role     @default(STUDENT)
  password  String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  accounts       Account[]
  sessions       Session[]
  studentProfile Student?
  teacherProfile Teacher?
  activityLogs   ActivityLog[]
  callRecords    CallRecord[]

  @@map("users")
}

model Student {
  id               String   @id @default(cuid())
  userId           String   @unique
  level            Level    @default(A1)
  branch           String
  emergencyContact String?
  photoUrl         String?
  dateOfBirth      DateTime?
  address          String?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  user        User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  enrollments Enrollment[]
  payments    Payment[]
  attendances Attendance[]
  assessments Assessment[]

  @@map("students")
}

model Teacher {
  id          String   @id @default(cuid())
  userId      String   @unique
  subject     String
  experience  Int?
  salary      Decimal?
  branch      String
  photoUrl    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)
  groups       Group[]
  classes      Class[]
  assignedLeads Lead[]

  @@map("teachers")
}

model Lead {
  id               String     @id @default(cuid())
  name             String
  phone            String     @unique
  coursePreference String
  status           LeadStatus @default(NEW)
  source           String?
  notes            String?
  assignedTo       String?
  followUpDate     DateTime?

  // Call management fields
  callStartedAt    DateTime?
  callEndedAt      DateTime?
  callDuration     Int?       // in seconds

  // Group assignment fields
  assignedGroupId  String?
  assignedTeacherId String?
  assignedAt       DateTime?

  // Archive management
  archivedAt       DateTime?

  createdAt        DateTime   @default(now())
  updatedAt        DateTime   @updatedAt

  // Relations
  assignedGroup    Group?     @relation(fields: [assignedGroupId], references: [id])
  assignedTeacher  Teacher?   @relation(fields: [assignedTeacherId], references: [id])
  callRecords      CallRecord[]

  @@map("leads")
}

model CallRecord {
  id          String   @id @default(cuid())
  leadId      String
  userId      String   // User who made the call
  startedAt   DateTime
  endedAt     DateTime?
  duration    Int?     // in seconds
  notes       String?
  recordingUrl String? // URL to call recording if available
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  lead        Lead     @relation(fields: [leadId], references: [id], onDelete: Cascade)
  user        User     @relation(fields: [userId], references: [id])

  @@map("call_records")
}

model Course {
  id          String   @id @default(cuid())
  name        String   @unique
  level       Level
  description String?
  duration    Int // in weeks
  price       Decimal
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  groups Group[]

  @@map("courses")
}

model Group {
  id        String   @id @default(cuid())
  name      String   @unique
  courseId  String
  teacherId String
  capacity  Int      @default(20)
  schedule  String // JSON string for schedule
  room      String?
  branch    String
  startDate DateTime
  endDate   DateTime
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  course      Course       @relation(fields: [courseId], references: [id])
  teacher     Teacher      @relation(fields: [teacherId], references: [id])
  enrollments Enrollment[]
  classes     Class[]
  assessments Assessment[]
  assignedLeads Lead[]

  @@map("groups")
}

model Enrollment {
  id        String           @id @default(cuid())
  studentId String
  groupId   String
  status    EnrollmentStatus @default(ACTIVE)
  startDate DateTime
  endDate   DateTime?
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  // Relations
  student Student @relation(fields: [studentId], references: [id])
  group   Group   @relation(fields: [groupId], references: [id])

  @@unique([studentId, groupId])
  @@map("enrollments")
}

model Class {
  id        String   @id @default(cuid())
  groupId   String
  teacherId String
  date      DateTime
  topic     String?
  homework  String?
  notes     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  group       Group        @relation(fields: [groupId], references: [id])
  teacher     Teacher      @relation(fields: [teacherId], references: [id])
  attendances Attendance[]

  @@map("classes")
}

model Attendance {
  id        String           @id @default(cuid())
  studentId String
  classId   String
  status    AttendanceStatus @default(PRESENT)
  notes     String?
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt

  // Relations
  student Student @relation(fields: [studentId], references: [id])
  class   Class   @relation(fields: [classId], references: [id])

  @@unique([studentId, classId])
  @@map("attendances")
}

model Payment {
  id            String        @id @default(cuid())
  studentId     String
  amount        Decimal
  method        PaymentMethod
  status        PaymentStatus @default(PENDING)
  description   String?
  transactionId String?
  dueDate       DateTime?
  paidDate      DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  student Student @relation(fields: [studentId], references: [id])

  @@map("payments")
}

model ActivityLog {
  id          String   @id @default(cuid())
  userId      String
  userRole    Role
  action      String   // e.g., "CREATE", "UPDATE", "DELETE", "VIEW"
  resource    String   // e.g., "student", "payment", "group"
  resourceId  String?  // ID of the affected resource
  details     Json?    // Additional details about the action
  ipAddress   String?
  userAgent   String?
  createdAt   DateTime @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@map("activity_logs")
}

model Assessment {
  id            String         @id @default(cuid())
  studentId     String?        // Null for group tests not yet assigned
  groupId       String?        // For group tests
  testName      String         // Name of the test that was administered
  type          AssessmentType
  level         Level?
  score         Int?
  maxScore      Int?
  passed        Boolean        @default(false)
  questions     Json?          // Store questions and answers
  results       Json?          // Store detailed results
  assignedBy    String?        // Teacher/Admin who assigned
  assignedAt    DateTime?      // When assigned to student/group
  startedAt     DateTime?      // When student started
  completedAt   DateTime?
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt

  // Relations
  student   Student? @relation(fields: [studentId], references: [id])
  group     Group?   @relation(fields: [groupId], references: [id])

  @@map("assessments")
}



enum Role {
  ADMIN
  MANAGER
  TEACHER
  RECEPTION
  CASHIER
  STUDENT
  PARENT
}

enum Level {
  A1
  A2
  B1
  B2
  C1
  C2
  IELTS_5_5
  IELTS_6_0
  IELTS_6_5
  IELTS_7_0
  SAT
  MATH
  KIDS
}

enum LeadStatus {
  NEW
  CALLING
  CALL_COMPLETED
  GROUP_ASSIGNED
  ARCHIVED
  NOT_INTERESTED
}

enum EnrollmentStatus {
  ACTIVE
  COMPLETED
  DROPPED
  SUSPENDED
}

enum AttendanceStatus {
  PRESENT
  ABSENT
  LATE
  EXCUSED
}

enum PaymentMethod {
  CASH
  UZCARD
  HUMO
  PAYME
  CLICK
  BANK_TRANSFER
}

enum PaymentStatus {
  PENDING
  PAID
  OVERDUE
  REFUNDED
}

enum AssessmentType {
  LEVEL_TEST
  PROGRESS_TEST
  FINAL_EXAM
  GROUP_TEST
}

enum AssessmentStatus {
  DRAFT
  ASSIGNED
  IN_PROGRESS
  COMPLETED
  EXPIRED
}
